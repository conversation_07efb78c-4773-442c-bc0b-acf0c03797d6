const LuaMinifier = require('./lua');

// Test code with various Roblox patterns
const testCode = `
-- Complex Roblox script with various patterns
local Players = game:GetService("Players")
local TweenService = game:GetService("TweenService")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer
local character = player.Character
local humanoid = character:FindFirstChild("Humanoid")

-- Custom variables that should be renamed
local myVariable = "test"
local customFunction = function(param1, param2)
    return param1 + param2
end

-- Roblox API usage
if humanoid then
    humanoid.WalkSpeed = 50
    humanoid.JumpPower = 100
    
    local humanoidRootPart = character.HumanoidRootPart
    if humanoidRootPart then
        local tweenInfo = TweenInfo.new(2, Enum.EasingStyle.Quad)
        local tween = TweenService:Create(humanoidRootPart, tweenInfo, {
            Position = Vector3.new(0, 10, 0),
            Size = Vector3.new(4, 4, 4)
        })
        tween:Play()
    end
end

-- Custom function with Roblox API
function teleportPlayer(position)
    local character = player.Character
    if character then
        local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
        if humanoidRootPart then
            humanoidRootPart.CFrame = CFrame.new(position)
            print("Player teleported to:", position)
        end
    end
end

-- For loop with custom variables
for i = 1, 10 do
    local customVar = i * 2
    print("Custom variable:", customVar)
end

-- Global assignment
globalVar = "This should be renamed"

-- Call custom function
teleportPlayer(Vector3.new(100, 50, 200))
customFunction(5, 10)
`;

console.log('=== Testing Auto-Detection System ===\n');

const minifier = new LuaMinifier();

try {
    console.log('Original code:');
    console.log('─'.repeat(80));
    console.log(testCode);
    console.log('─'.repeat(80));
    
    // Reset minifier state
    minifier.reset();
    
    // Perform minification with new auto-detection system
    const minified = minifier.minify(testCode, {
        removeComments: true,
        minifyWhitespace: true,
        renameVariables: true,
        renameFunctions: true
    });
    
    console.log('\nMinified and obfuscated code:');
    console.log('─'.repeat(80));
    console.log(minified);
    console.log('─'.repeat(80));
    
    console.log('\nAuto-detected protected identifiers:');
    console.log('─'.repeat(40));
    const protectedArray = Array.from(minifier.protectedIdentifiers).sort();
    protectedArray.forEach(id => {
        if (id === 'position') {
            console.log(`  🛡️  ${id} ⚠️  (This should NOT be protected!)`);
        } else {
            console.log(`  🛡️  ${id}`);
        }
    });
    
    console.log('\nVariable mappings (renamed):');
    console.log('─'.repeat(40));
    for (const [original, renamed] of minifier.variableMap.entries()) {
        console.log(`  ${original} → ${renamed}`);
    }
    
    console.log('\nFunction mappings (renamed):');
    console.log('─'.repeat(40));
    for (const [original, renamed] of minifier.functionMap.entries()) {
        console.log(`  ${original} → ${renamed}`);
    }
    
    // Validation checks
    console.log('\nValidation checks:');
    console.log('─'.repeat(40));
    
    const checks = [
        {
            name: 'Service calls preserved',
            test: () => minified.includes('game:GetService("Players")') && 
                        minified.includes('game:GetService("TweenService")') &&
                        minified.includes('game:GetService("RunService")')
        },
        {
            name: 'Roblox APIs preserved',
            test: () => minified.includes('Vector3.new') && 
                        minified.includes('CFrame.new') && 
                        minified.includes('TweenInfo.new') &&
                        minified.includes('Enum.EasingStyle')
        },
        {
            name: 'Roblox properties preserved',
            test: () => minified.includes('.LocalPlayer') && 
                        minified.includes('.Character') && 
                        minified.includes('.WalkSpeed') &&
                        minified.includes('.JumpPower')
        },
        {
            name: 'Roblox methods preserved',
            test: () => minified.includes(':FindFirstChild') && 
                        minified.includes(':Create') && 
                        minified.includes(':Play')
        },
        {
            name: 'Custom variables renamed',
            test: () => !minified.includes('myVariable') && 
                        !minified.includes('customVar') && 
                        !minified.includes('globalVar')
        },
        {
            name: 'Custom functions renamed',
            test: () => !minified.includes('customFunction') && 
                        !minified.includes('teleportPlayer')
        },
        {
            name: 'Function parameters renamed',
            test: () => {
                // Check that param1 and param2 are renamed
                const param1Renamed = !minified.includes('param1');
                const param2Renamed = !minified.includes('param2');

                // Check that position parameter is renamed (not in function signature)
                const positionInFunction = /function\s+\w+\s*\(\s*position\s*\)/.test(minified);
                const positionRenamed = !positionInFunction;

                return param1Renamed && param2Renamed && positionRenamed;
            }
        }
    ];
    
    let allPassed = true;
    for (const check of checks) {
        const passed = check.test();
        console.log(`  ${passed ? '✅' : '❌'} ${check.name}`);
        if (!passed) allPassed = false;
    }
    
    console.log('\n' + '='.repeat(80));
    if (allPassed) {
        console.log('🎉 SUCCESS: Auto-detection system working perfectly!');
        console.log('✅ All Roblox APIs are protected');
        console.log('✅ All custom identifiers are renamed');
        console.log('✅ No hardcoded lists needed!');
    } else {
        console.log('⚠️  Some checks failed - system needs adjustment');
    }
    
} catch (error) {
    console.error('Test failed:', error.message);
    console.error(error.stack);
}
